(function (window, undefined) {
  var checkedValue = 0;

  function initPlugin() {
    document.querySelectorAll('.title-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', function () {
        if (this.checked) {
          const currentIndex = parseInt(this.getAttribute('data-index'));
          checkedValue = currentIndex;

          for (let i = 1; i < currentIndex; i++) {
            const prevCheckbox = document.querySelector(`.title-checkbox[data-index="${i}"]`);
            prevCheckbox.checked = true;
          }
        } else {
          const currentIndex = parseInt(this.getAttribute('data-index'));
          for (let i = currentIndex + 1; i <= 5; i++) {
            const nextCheckbox = document.querySelector(`.title-checkbox[data-index="${i}"]`);
            nextCheckbox.checked = false;
          }

          // 重新计算最高选中的级别
          checkedValue = 0;
          for (let i = 5; i >= 1; i--) {
            const checkbox = document.querySelector(`.title-checkbox[data-index="${i}"]`);
            if (checkbox.checked) {
              checkedValue = i;
              break;
            }
          }
        }
      });
    });
  }

  function setTOC() {
    Asc.scope.outlineLvls = checkedValue + 1;
    window.Asc.plugin.callCommand(
      function () {
        let oDocument = Api.GetDocument();
        let allParagraphs = oDocument.GetAllParagraphs();
        let allHeadingParagraphs = oDocument.GetAllHeadingParagraphs();
        var tocIndex = -1;
        var tocParagraph = null;
        var forewordPage = -1;
        var introductionPage = -1;
        var standardPage = -1;
        var contentStartIndex = -1;
        var indexes = -1;

        function romanToInt(num) {
          const roman = { I: 1, V: 5, X: 10, L: 50 };
          let result = 0;

          for (let i = 0; i < num.length; i++) {
            const currentVal = roman[num[i]];
            const nextVal = roman[num[i + 1]];

            if (nextVal > currentVal) {
              result += nextVal - currentVal;
              i++;
            } else {
              result += currentVal;
            }
          }
          return result;
        }

        for (var i = 0; i < allParagraphs.length; i++) {
          var paragraph = allParagraphs[i];
          var text = paragraph.GetText().trim();
          var style = paragraph?.GetStyle()?.GetName();

          if (text === '目次' && style === '标准文件_目录标题') {
            tocIndex = i;
            tocParagraph = paragraph;
          }

          if (text === '前言' && style === '标准文件_前言、引言标题')
            forewordPage = romanToInt(paragraph?.GetRange()?.GetStartPage());

          if (text === '引言' && style === '标准文件_前言、引言标题')
            introductionPage = romanToInt(paragraph?.GetRange()?.GetStartPage());

          if (style === '标准文件_正文标准名称') standardPage = paragraph?.GetRange()?.GetStartPage();

          if (contentStartIndex === -1 && (forewordPage != -1 || introductionPage != -1 || standardPage != -1))
            contentStartIndex = i;

          if (text === '索引' && style === '标准文件_索引标题') indexes = i;

          if (indexes !== -1) break;
        }

        if (contentStartIndex === -1) {
          return { error: '未找到正文标题' };
        }

        if (tocIndex < contentStartIndex) {
          for (var i = contentStartIndex - 1; i > tocIndex; i--) {
            if (allParagraphs[i].GetText().trim() != '') allParagraphs[i].Delete();
          }
        }

        function intToRoman(num) {
          const val = [50, 40, 10, 9, 5, 4, 1];
          const sym = ['L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
          let roman = '';

          for (let i = 0; i < val.length; i++) {
            while (num >= val[i]) {
              roman += sym[i];
              num -= val[i];
            }
          }

          return roman;
        }

        let nParagraphs = [];
        allHeadingParagraphs.forEach(paragraph => {
          let text = paragraph.GetText().trim();
          let paragraphStyle = paragraph?.GetStyle()?.GetName();
          let oParagraphLevelIndex = paragraph?.GetNumbering()?.GetLevelIndex();
          let indexesStyle = 'toc ' + (!oParagraphLevelIndex || Number(oParagraphLevelIndex) <= 1 ? '1' : oParagraphLevelIndex);
          let currentPage = paragraph?.GetRange()?.GetStartPage() || 0;
          const romanRegex = /^[IVXLCDM]+$/i;
          currentPage = romanRegex.test(currentPage) ? romanToInt(paragraph?.GetRange()?.GetStartPage()) : currentPage;

          if (currentPage < standardPage) {
            currentPage = intToRoman(currentPage);
          } else {
            currentPage = currentPage - standardPage + 1;
          }

          let shouldInclude = false;
          if (oParagraphLevelIndex && oParagraphLevelIndex <= Asc.scope.outlineLvls) {
            shouldInclude = true;
          }
          if (paragraphStyle === '标准文件_参考文献标题' || paragraphStyle === '标准文件_索引标题') {
            shouldInclude = true;
            indexesStyle = 'toc 1';
          }
          if (shouldInclude) {
            nParagraphs.unshift({
              currentPage: currentPage.toString(),
              text: text,
              oParagraphLevelIndex: oParagraphLevelIndex,
              indexesStyle: indexesStyle,
              paragraphStyle: paragraphStyle,
            });
          }
        });

        if (nParagraphs.length > 0) {
          nParagraphs.forEach(item => {
            var nParagraph = Api.CreateParagraph();
            nParagraph.AddText(item.text);
            nParagraph.SetStyle(oDocument.GetStyle(item.indexesStyle));
            nParagraph.AddTabStop();
            nParagraph.AddText(item.currentPage || '0');
            tocParagraph.InsertParagraph(nParagraph, 'after');
          });
          allParagraphs[tocIndex].Select();
          return;
        } else {
          return { error: '未查找到目次内容' };
        }
      },
      false,
      true,
      function (result) {
        if (result && result.error) {
          window.Asc.plugin.executeMethod('ShowError', [result.error]);
        }
        window.Asc.plugin.executeMethod('EndAction', ['Block', 'Save to local storage...', '']);
        window.Asc.plugin.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    initPlugin();
  };

  window.Asc.plugin.button = function (id) {
    if (id == '1') {
      setTOC.call(this);
    } else {
      window.Asc.plugin.executeCommand('close', '');
    }
  };
})(window, undefined);
