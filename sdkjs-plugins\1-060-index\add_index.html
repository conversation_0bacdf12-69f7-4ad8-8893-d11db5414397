<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>添加索引项</title>
    <script type="text/javascript" src="../v1/plugins.js"></script>
    <script type="text/javascript" src="../v1/plugins-ui.js"></script>
    <link rel="stylesheet" href="../v1/plugins.css" />
    <script type="text/javascript" src="vendor/pinyin-pro/pinyin-pro.js"></script>
    <script type="text/javascript" src="scripts/add_index.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
      }

      .form-container {
        margin: 15px 20px;
      }

      .form-item {
        margin-bottom: 15px;
      }

      .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #374151;
        font-size: 13px;
      }

      .form-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        font-size: 13px;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }

      .form-input::placeholder {
        color: #9ca3af;
      }

      .search-buttons {
        margin-top: 15px;
      }

      .search-button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        background-color: #3b82f6;
        color: white;
      }

      .search-button:hover:not(:disabled) {
        background-color: #2563eb;
      }

      .search-button:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
        opacity: 0.6;
      }

      .form-input.error {
        border-color: #ef4444;
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
      }

      .form-input.success {
        border-color: #10b981;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
      }

      .input-hint {
        margin-top: 4px;
        font-size: 11px;
        color: #6b7280;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <div class="form-container">
      <div class="form-item">
        <label class="form-label" for="search-text">查找内容：</label>
        <input type="text" id="search-text" class="form-input" placeholder="请输入要查找的内容" maxlength="30" />
      </div>
      <div class="search-buttons">
        <button id="search-next" class="search-button">查找下一处</button>
      </div>
    </div>
  </body>
</html>
