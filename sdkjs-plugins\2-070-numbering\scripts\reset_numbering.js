(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';

            if (!currentSentence) {
              return { error: '未找到当前句子' };
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              oDocument.ReplaceCurrentSentence(currentSentence);
              return { error: '未找到当前段落' };
            }

            return targetPosition;
          }

          let currentPosition = getCurrentParagraphPosition();
          let allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return { error: '段落位置超出范围' };
          }

          let currentParagraph = allParagraphs[currentPosition];
          let currentStyle = currentParagraph.GetStyle().GetName();
          let currentNumbering = currentParagraph.GetNumbering();
          let currentLevel = currentNumbering.GetLevelIndex();
          let prevNumberingArray = [];

          if (currentNumbering && currentNumbering.GetClassType() && currentNumbering.GetClassType() === 'numberingLevel') {
            let numLvl = currentParagraph.GetNumbering();
            let newNum = Api.FromJSON(numLvl.GetNumbering().ToJSON());
            currentParagraph.SetNumbering(newNum.GetLevel(numLvl.GetLevelIndex()));
            for (var i = currentPosition + 1; i < allParagraphs.length; i++) {
              if (allParagraphs[i].GetNumbering() && allParagraphs[i].GetNumbering().GetClassType() === 'numberingLevel') {
                let nextStyle = allParagraphs[i].GetStyle().GetName();
                let nextLevel = allParagraphs[i].GetNumbering().GetLevelIndex();
                if ((currentStyle != '标准文件_章标题' && nextStyle == '标准文件_章标题') || (currentStyle != '标准文件_章标题' && nextLevel < currentLevel)) break;
                console.log(allParagraphs[i].GetText());
                if (nextLevel == currentLevel && nextStyle == currentStyle) {
                  prevNumberingArray.push(i);
                }
              }
            }
            prevNumberingArray.forEach((item, index) => {
              var prevNumbering =
                index == 0
                  ? currentParagraph.GetNumbering().GetNumbering()
                  : allParagraphs[prevNumberingArray[index - 1]].GetNumbering().GetNumbering();
              var prevNumberingObj = prevNumbering.GetLevel(allParagraphs[item].GetNumbering().GetLevelIndex());
              allParagraphs[item].SetNumbering(prevNumberingObj);
            });

            return;
          } else {
            return { error: '当前段落无编号信息！' };
          }
        } catch (e) {
          console.error('发生错误:', e);
          return { error: e.message };
        }
      },
      false,
      true,
      function (result) {
        if (result && result.error) {
          this.executeMethod('ShowError', [result.error]);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function () {};
})(window, undefined);
