<!--
 (c) Copyright Ascensio System SIA 2020

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>TOC</title>
    <script type="text/javascript" src="../v1/plugins.js"></script>
    <script type="text/javascript" src="../v1/plugins-ui.js"></script>
    <link rel="stylesheet" href="../v1/plugins.css">
    <link rel="stylesheet" href="styles/style.css" />
    <script type="text/javascript" src="scripts/TOC.js"></script>
</head>
<body>
    <div class="plugin-container">
      <div class="plugin-content">
        <!-- 脚注内容输入框 -->
        <div class="form-group" style="margin-top:1px;">
          <div style="margin-bottom:10px;">
            <div>默认目次内容包括：</div>
            <div style="color:#999;padding-top:5px;padding-left:10px;">前言、引言、章节、附录、参考文献、索引。</div>
          </div>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input type="checkbox" name="options" data-index="1" class="title-checkbox" id="item1" />
              <span class="checkbox-text">一级条标题</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" name="options" data-index="2" class="title-checkbox" id="item2" />
              <span class="checkbox-text">二级条标题</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" name="options" data-index="3" class="title-checkbox" id="item3" />
              <span class="checkbox-text">三级条标题</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" name="options" data-index="4" class="title-checkbox" id="item4" />
              <span class="checkbox-text">四级条标题</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" name="options" data-index="5" class="title-checkbox" id="item5" />
              <span class="checkbox-text">五级条标题</span>
            </label>
          </div>
        </div>
    </div>
</body>
</html>